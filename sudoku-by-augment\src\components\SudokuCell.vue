<script setup>
import { computed } from 'vue';

const props = defineProps({
  value: {
    type: Number,
    default: 0
  },
  isOriginal: {
    type: Boolean,
    default: false
  },
  row: {
    type: Number,
    required: true
  },
  col: {
    type: Number,
    required: true
  },
  isError: {
    type: Boolean,
    default: false
  },
  correctValue: {
    type: Number,
    default: 0
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update', 'select']);

// Determine if this cell is at a box boundary (for thicker borders)
const isRightBorder = computed(() => (props.col + 1) % 3 === 0 && props.col < 8);
const isBottomBorder = computed(() => (props.row + 1) % 3 === 0 && props.row < 8);

// Determine cell display value
const displayValue = computed(() => props.value === 0 ? '' : props.value);

// Determine text color based on cell state
const textColor = computed(() => {
  if (props.isOriginal) return 'black';
  if (props.isError) return 'red';
  return '#1565C0'; // Royal blue for user input
});

// Handle user input
function handleInput(event) {
  if (props.isOriginal) return;

  const value = event.target.value;

  // Validate input: only allow numbers 1-9 or empty
  if (value === '' || (parseInt(value) >= 1 && parseInt(value) <= 9)) {
    emit('update', props.row, props.col, value === '' ? 0 : parseInt(value));
  } else {
    // Reset invalid input
    event.target.value = displayValue.value;
  }
}

// Handle cell click for selection
function handleCellClick() {
  if (!props.isOriginal) {
    emit('select', props.row, props.col);
  }
}
</script>

<template>
  <div
    class="sudoku-cell"
    :class="{
      'right-border': isRightBorder,
      'bottom-border': isBottomBorder,
      'original': isOriginal,
      'error': isError,
      'selected': isSelected
    }"
    @click="handleCellClick"
  >
    <input
      v-if="!isOriginal"
      type="number"
      min="1"
      max="9"
      :value="displayValue"
      @input="handleInput"
      class="cell-input"
      :style="{ color: textColor }"
    />
    <span v-else class="cell-value" :style="{ color: textColor }">{{ displayValue }}</span>
  </div>
</template>

<style scoped>
.sudoku-cell {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  box-sizing: border-box;
  position: relative;
}

.right-border {
  border-right: 2px solid black;
}

.bottom-border {
  border-bottom: 2px solid black;
}

.cell-input {
  width: 100%;
  height: 100%;
  border: none;
  text-align: center;
  font-size: 18px;
  background: transparent;
  outline: none;
}

.cell-value {
  font-size: 18px;
  font-weight: bold;
}

.original {
  background-color: #f5f5f5;
}

.error {
  background-color: #ffebee;
}

.selected {
  background-color: #e3f2fd;
  box-shadow: inset 0 0 0 2px #2196F3;
}

/* Remove spinner from number input */
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}
</style>
