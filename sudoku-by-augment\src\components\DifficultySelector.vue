<script setup>
import { ref } from 'vue';

const emit = defineEmits(['start-game']);
const selectedDifficulty = ref('easy');

const difficulties = [
  { value: 'easy', label: '簡單', color: 'success' },
  { value: 'medium', label: '中等', color: 'warning' },
  { value: 'hard', label: '困難', color: 'error' }
];

function startGame() {
  emit('start-game', selectedDifficulty.value);
}
</script>

<template>
  <v-card class="pa-6 rounded-lg" width="400" elevation="8">
    <v-card-title class="text-h5 text-center mb-4">選擇難度</v-card-title>
    
    <v-card-text>
      <v-radio-group v-model="selectedDifficulty" class="mb-4">
        <v-radio
          v-for="difficulty in difficulties"
          :key="difficulty.value"
          :value="difficulty.value"
          :label="difficulty.label"
          :color="difficulty.color"
        ></v-radio>
      </v-radio-group>
    </v-card-text>
    
    <v-card-actions class="justify-center">
      <v-btn
        color="primary"
        size="large"
        @click="startGame"
      >
        開始遊戲
      </v-btn>
    </v-card-actions>
  </v-card>
</template>
