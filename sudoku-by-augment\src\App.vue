<script setup>
import { ref, onMounted } from 'vue';
import SudokuBoard from './components/SudokuBoard.vue';
import DifficultySelector from './components/DifficultySelector.vue';

const difficulty = ref('easy');
const gameStarted = ref(false);

function startGame(selectedDifficulty) {
  difficulty.value = selectedDifficulty;
  gameStarted.value = true;
}

function resetGame() {
  gameStarted.value = false;
}
</script>

<template>
  <v-app>
    <v-main class="sudoku-background">
      <v-container class="d-flex flex-column align-center justify-center fill-height">
        <h1 class="text-h3 mb-6 text-white">數獨遊戲</h1>

        <template v-if="!gameStarted">
          <DifficultySelector @start-game="startGame" />
        </template>

        <template v-else>
          <SudokuBoard :difficulty="difficulty" />
          <v-btn color="error" class="mt-6" @click="resetGame">
            重新開始
          </v-btn>
        </template>
      </v-container>
    </v-main>
  </v-app>
</template>

<style>
.sudoku-background {
  background: linear-gradient(to bottom right, #000000, #0d47a1) !important;
  min-height: 100vh;
  width: 100%;
}

/* Override Vuetify default styles */
:deep(.v-application) {
  background: transparent !important;
}

:deep(.v-main) {
  background: linear-gradient(to bottom right, #000000, #0d47a1) !important;
}
</style>
