<script setup>
import { computed } from 'vue';

const props = defineProps({
  board: {
    type: Array,
    required: true
  },
  selectedCell: {
    type: Object,
    default: null
  },
  gameOver: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update-cell']);

// Calculate how many of each number are already on the board
const numberCounts = computed(() => {
  const counts = Array(10).fill(0); // 0-9, but we'll ignore 0
  
  for (const row of props.board) {
    for (const cell of row) {
      if (cell.userValue > 0) {
        counts[cell.userValue]++;
      }
    }
  }
  
  return counts;
});

// Check if a number is fully used (appears 9 times on the board)
const isNumberFullyUsed = computed(() => {
  return Array(10).fill(false).map((_, index) => {
    if (index === 0) return true; // 0 is not a valid input
    return numberCounts.value[index] >= 9;
  });
});

// Handle number button click
function handleNumberClick(number) {
  if (props.selectedCell && !isNumberFullyUsed.value[number] && !props.gameOver) {
    emit('update-cell', props.selectedCell.row, props.selectedCell.col, number);
  }
}

// Handle clear button click
function handleClearClick() {
  if (props.selectedCell && !props.gameOver) {
    emit('update-cell', props.selectedCell.row, props.selectedCell.col, 0);
  }
}
</script>

<template>
  <div class="number-pad-container mt-4">
    <v-card elevation="4" class="pa-2">
      <div class="number-pad">
        <v-btn
          v-for="number in 9"
          :key="number"
          :disabled="isNumberFullyUsed[number] || !selectedCell || gameOver"
          :color="isNumberFullyUsed[number] ? 'grey' : 'primary'"
          class="number-button"
          @click="handleNumberClick(number)"
        >
          {{ number }}
        </v-btn>
        <v-btn
          color="error"
          class="number-button"
          :disabled="!selectedCell || gameOver"
          @click="handleClearClick"
        >
          清除
        </v-btn>
      </div>
    </v-card>
  </div>
</template>

<style scoped>
.number-pad-container {
  width: 100%;
  max-width: 400px;
}

.number-pad {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.number-button {
  font-size: 18px;
  font-weight: bold;
}
</style>
