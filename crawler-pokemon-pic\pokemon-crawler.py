import os
import requests
from bs4 import BeautifulSoup
import urllib.parse
import tempfile
import shutil

def main():
    # 建立儲存圖片的資料夾
    if not os.path.exists('pic'):
        os.makedirs('pic')
    
    # 寶可夢圖鑑網址
    url = "https://tw.portal-pokemon.com/play/pokedex"
    
    # 發送請求獲取網頁內容
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 找到所有寶可夢卡片元素
    pokemon_cards = soup.select('.pokemon-list .pokemon-card')
    
    for card in pokemon_cards:
        try:
            # 獲取寶可夢編號
            number = card.select_one('.pokemon-number').text.strip()
            
            # 獲取寶可夢名稱
            name = card.select_one('.pokemon-name').text.strip()
            
            # 獲取備註 (如果有的話)
            note_element = card.select_one('.pokemon-note')
            note = note_element.text.strip() if note_element else None
            
            # 獲取圖片URL
            img_url = card.select_one('img')['src']
            if not img_url.startswith('http'):
                img_url = urllib.parse.urljoin(url, img_url)
            
            # 建立檔案名
            if note:
                filename = f"{number}-{name}-{note}.jpg"
            else:
                filename = f"{number}-{name}.jpg"
            
            # 下載圖片
            img_response = requests.get(img_url, stream=True)
            if img_response.status_code == 200:
                with open(os.path.join('pic', filename), 'wb') as f:
                    shutil.copyfileobj(img_response.raw, f)
                print(f"已下載: {filename}")
            else:
                print(f"無法下載 {name} 的圖片")
        
        except Exception as e:
            print(f"處理寶可夢時發生錯誤: {e}")
    
    print("下載完成!")

if __name__ == "__main__":
    main()