import os
import requests
from bs4 import BeautifulSoup
import urllib.parse
import tempfile
import shutil
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def main():
    # 建立儲存圖片的資料夾
    if not os.path.exists('pic'):
        os.makedirs('pic')

    # 設定Chrome選項
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 無頭模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')

    # 啟動瀏覽器
    driver = webdriver.Chrome(options=chrome_options)

    try:
        # 寶可夢圖鑑網址
        url = "https://tw.portal-pokemon.com/play/pokedex"
        print("正在載入網頁...")
        driver.get(url)

        # 等待頁面載入
        time.sleep(5)

        # 嘗試點擊"查看更多"按鈕來載入所有寶可夢
        try:
            load_more_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CLASS_NAME, "pokedex-more"))
            )
            while load_more_button:
                driver.execute_script("arguments[0].click();", load_more_button)
                time.sleep(2)
                try:
                    load_more_button = driver.find_element(By.CLASS_NAME, "pokedex-more")
                except:
                    break
        except:
            print("沒有找到'查看更多'按鈕，繼續處理現有內容...")

        # 獲取頁面HTML
        soup = BeautifulSoup(driver.page_source, 'html.parser')

        # 嘗試不同的選擇器
        pokemon_cards = soup.select('.pokemon-list--box')
        if not pokemon_cards:
            pokemon_cards = soup.select('.pokedex-pokemon')
        if not pokemon_cards:
            pokemon_cards = soup.select('[class*="pokemon"]')

        print(f"找到 {len(pokemon_cards)} 個寶可夢卡片")

        # 如果還是找不到，嘗試調試HTML結構
        if len(pokemon_cards) == 0:
            print("正在分析網頁結構...")
            # 保存HTML到文件以便調試
            with open('debug.html', 'w', encoding='utf-8') as f:
                f.write(driver.page_source)
            print("HTML已保存到debug.html，請檢查網頁結構")

            # 嘗試找到任何包含圖片的元素
            all_imgs = soup.find_all('img')
            print(f"找到 {len(all_imgs)} 個圖片元素")

            # 列出前幾個圖片的src
            for i, img in enumerate(all_imgs[:5]):
                src = img.get('src', 'No src')
                alt = img.get('alt', 'No alt')
                print(f"圖片 {i+1}: src={src}, alt={alt}")

        for card in pokemon_cards:
            try:
                # 嘗試不同的選擇器來獲取寶可夢資訊
                number_element = (card.select_one('.pokemon-list--box__no') or
                                card.select_one('.pokemon-number') or
                                card.select_one('[class*="number"]'))

                name_element = (card.select_one('.pokemon-list--box__name') or
                              card.select_one('.pokemon-name') or
                              card.select_one('[class*="name"]'))

                if not number_element or not name_element:
                    print(f"無法找到寶可夢編號或名稱元素")
                    continue

                number = number_element.text.strip()
                name = name_element.text.strip()

                # 獲取備註 (如果有的話)
                note_element = card.select_one('.pokemon-list--box__subname')
                note = note_element.text.strip() if note_element else None

                # 獲取圖片URL
                img_element = card.select_one('img')
                if not img_element:
                    print(f"無法找到 {name} 的圖片元素")
                    continue

                img_url = img_element.get('src') or img_element.get('data-src')
                if not img_url:
                    print(f"無法獲取 {name} 的圖片URL")
                    continue

                if not img_url.startswith('http'):
                    img_url = urllib.parse.urljoin(url, img_url)

                # 建立檔案名
                if note:
                    filename = f"{number}-{name}-{note}.jpg"
                else:
                    filename = f"{number}-{name}.jpg"

                # 清理檔案名中的非法字符
                filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()

                # 下載圖片
                img_response = requests.get(img_url, stream=True)
                if img_response.status_code == 200:
                    with open(os.path.join('pic', filename), 'wb') as f:
                        shutil.copyfileobj(img_response.raw, f)
                    print(f"已下載: {filename}")
                else:
                    print(f"無法下載 {name} 的圖片 (狀態碼: {img_response.status_code})")

            except Exception as e:
                print(f"處理寶可夢時發生錯誤: {e}")

        print("下載完成!")

    finally:
        # 關閉瀏覽器
        driver.quit()

if __name__ == "__main__":
    main()