<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue';
import SudokuCell from './SudokuCell.vue';
import NumberPad from './NumberPad.vue';

const props = defineProps({
  difficulty: {
    type: String,
    default: 'easy'
  }
});

const board = ref([]);
const solution = ref([]);
const isComplete = ref(false);
const isValid = ref(true);
const errorCount = ref(0);
const maxErrors = ref(3);
const remainingErrors = computed(() => maxErrors.value - errorCount.value);
const gameOver = computed(() => errorCount.value >= maxErrors.value);

// Timer related variables
const startTime = ref(null);
const elapsedTime = ref(0);
const timerInterval = ref(null);
const formattedTime = computed(() => {
  const minutes = Math.floor(elapsedTime.value / 60);
  const seconds = elapsedTime.value % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
});

// Selected cell tracking
const selectedCell = ref(null);

// Difficulty settings - number of cells to reveal
const difficultyCellsMap = {
  easy: 45,
  medium: 35,
  hard: 25
};

// Start the timer
function startTimer() {
  // Clear any existing timer
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
  }

  // Reset timer values
  startTime.value = Date.now();
  elapsedTime.value = 0;

  // Start the timer interval
  timerInterval.value = setInterval(() => {
    elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000);
  }, 1000);
}

// Stop the timer
function stopTimer() {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
}

// Initialize the board
function initializeBoard() {
  // Reset state
  isComplete.value = false;
  isValid.value = true;
  errorCount.value = 0;
  selectedCell.value = null;

  // Generate a solved Sudoku board
  solution.value = generateSolvedBoard();

  // Create a playable board by hiding some cells based on difficulty
  const cellsToReveal = difficultyCellsMap[props.difficulty];
  board.value = createPlayableBoard(solution.value, cellsToReveal);

  // Start the timer
  startTimer();
}

// Generate a solved Sudoku board
function generateSolvedBoard() {
  // Create an empty 9x9 board
  const solvedBoard = Array(9).fill().map(() => Array(9).fill(0));

  // Use backtracking to fill the board
  solveSudoku(solvedBoard);

  return solvedBoard;
}

// Solve the Sudoku using backtracking algorithm
function solveSudoku(board) {
  for (let row = 0; row < 9; row++) {
    for (let col = 0; col < 9; col++) {
      if (board[row][col] === 0) {
        // Try placing numbers 1-9
        const nums = shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);
        for (const num of nums) {
          if (isValidPlacement(board, row, col, num)) {
            board[row][col] = num;

            if (solveSudoku(board)) {
              return true;
            }

            // If placing this number doesn't lead to a solution, backtrack
            board[row][col] = 0;
          }
        }
        return false; // No valid number can be placed
      }
    }
  }
  return true; // Board is filled
}

// Check if placing a number at a position is valid
function isValidPlacement(board, row, col, num) {
  // Check row
  for (let i = 0; i < 9; i++) {
    if (board[row][i] === num) return false;
  }

  // Check column
  for (let i = 0; i < 9; i++) {
    if (board[i][col] === num) return false;
  }

  // Check 3x3 box
  const boxRow = Math.floor(row / 3) * 3;
  const boxCol = Math.floor(col / 3) * 3;
  for (let i = 0; i < 3; i++) {
    for (let j = 0; j < 3; j++) {
      if (board[boxRow + i][boxCol + j] === num) return false;
    }
  }

  return true;
}

// Create a playable board by hiding some cells
function createPlayableBoard(solvedBoard, cellsToReveal) {
  // Create a deep copy of the solved board
  const playableBoard = solvedBoard.map(row => [...row]);

  // Calculate cells to reveal per region to ensure even distribution
  const totalRegions = 9; // 9 regions (3x3 boxes)
  const cellsPerRegion = Math.floor(cellsToReveal / totalRegions);
  const extraCells = cellsToReveal % totalRegions;

  // Create a map to track revealed cells in each region
  const revealedInRegion = Array(totalRegions).fill(0);

  // First, hide all cells
  for (let row = 0; row < 9; row++) {
    for (let col = 0; col < 9; col++) {
      playableBoard[row][col] = 0;
    }
  }

  // Then reveal cells evenly across regions
  for (let region = 0; region < totalRegions; region++) {
    const boxRow = Math.floor(region / 3);
    const boxCol = region % 3;

    // Get all positions in this region
    const regionPositions = [];
    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 3; j++) {
        const row = boxRow * 3 + i;
        const col = boxCol * 3 + j;
        regionPositions.push({ row, col });
      }
    }

    // Shuffle positions within this region
    const shuffledPositions = shuffleArray(regionPositions);

    // Calculate how many cells to reveal in this region
    const cellsToRevealInRegion = cellsPerRegion + (region < extraCells ? 1 : 0);

    // Reveal cells in this region
    for (let i = 0; i < cellsToRevealInRegion; i++) {
      if (i < shuffledPositions.length) {
        const { row, col } = shuffledPositions[i];
        playableBoard[row][col] = solvedBoard[row][col];
      }
    }
  }

  return playableBoard.map((row, rowIndex) =>
    row.map((cell, colIndex) => ({
      value: cell,
      isOriginal: cell !== 0,
      userValue: cell,
      isError: false,
      correctValue: solvedBoard[rowIndex][colIndex]
    }))
  );
}

// Helper function to shuffle an array
function shuffleArray(array) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Select a cell
function selectCell(row, col) {
  if (!board.value[row][col].isOriginal && !gameOver.value && !isComplete.value) {
    selectedCell.value = { row, col };
  }
}

// Update a cell with user input
function updateCell(row, col, value) {
  if (!board.value[row][col].isOriginal && !gameOver.value) {
    const cell = board.value[row][col];
    const previousValue = cell.userValue;

    // Reset error state
    cell.isError = false;

    // Update the cell value
    cell.userValue = value;

    // If the user entered a value (not clearing)
    if (value !== 0) {
      // Check if the value is correct
      if (value !== solution.value[row][col]) {
        cell.isError = true;
        errorCount.value++;
      }
    }

    checkBoardStatus();

    // If game is complete or over, stop the timer
    if (isComplete.value || gameOver.value) {
      stopTimer();
    }
  }
}

// Check if the board is complete and valid
function checkBoardStatus() {
  // Check if all cells are filled
  const allFilled = board.value.every(row =>
    row.every(cell => cell.userValue !== 0)
  );

  // Check if the current board matches the solution
  const isCorrect = board.value.every((row, rowIndex) =>
    row.every((cell, colIndex) =>
      cell.userValue === solution.value[rowIndex][colIndex]
    )
  );

  isComplete.value = allFilled && isCorrect;
  isValid.value = checkBoardValidity();
}

// Check if the current board state is valid according to Sudoku rules
function checkBoardValidity() {
  // Check rows
  for (let row = 0; row < 9; row++) {
    const rowValues = board.value[row]
      .map(cell => cell.userValue)
      .filter(val => val !== 0);
    if (hasDuplicates(rowValues)) return false;
  }

  // Check columns
  for (let col = 0; col < 9; col++) {
    const colValues = board.value
      .map(row => row[col].userValue)
      .filter(val => val !== 0);
    if (hasDuplicates(colValues)) return false;
  }

  // Check 3x3 boxes
  for (let boxRow = 0; boxRow < 3; boxRow++) {
    for (let boxCol = 0; boxCol < 3; boxCol++) {
      const boxValues = [];
      for (let i = 0; i < 3; i++) {
        for (let j = 0; j < 3; j++) {
          const value = board.value[boxRow * 3 + i][boxCol * 3 + j].userValue;
          if (value !== 0) boxValues.push(value);
        }
      }
      if (hasDuplicates(boxValues)) return false;
    }
  }

  return true;
}

// Helper function to check for duplicates in an array
function hasDuplicates(array) {
  return new Set(array).size !== array.length;
}

// Watch for difficulty changes
watch(() => props.difficulty, () => {
  initializeBoard();
});

// Initialize the board when the component is mounted
onMounted(() => {
  initializeBoard();
});

// Clean up timer when component is unmounted
onUnmounted(() => {
  stopTimer();
});
</script>

<template>
  <div class="sudoku-container">
    <div class="game-header mb-4">
      <v-chip
        color="primary"
        size="large"
        class="game-timer me-4"
      >
        遊戲時間: {{ formattedTime }}
      </v-chip>
      <v-chip
        color="error"
        size="large"
        class="remaining-errors"
      >
        剩餘錯誤次數: {{ remainingErrors }}
      </v-chip>
    </div>

    <v-card class="sudoku-board" elevation="8">
      <div class="board-grid">
        <div v-for="(row, rowIndex) in board" :key="`row-${rowIndex}`" class="board-row">
          <SudokuCell
            v-for="(cell, colIndex) in row"
            :key="`cell-${rowIndex}-${colIndex}`"
            :value="cell.userValue"
            :is-original="cell.isOriginal"
            :is-error="cell.isError"
            :correct-value="cell.correctValue"
            :row="rowIndex"
            :col="colIndex"
            :is-selected="selectedCell && selectedCell.row === rowIndex && selectedCell.col === colIndex"
            @update="updateCell"
            @select="selectCell"
          />
        </div>
      </div>
    </v-card>

    <!-- Number Pad -->
    <NumberPad
      :board="board"
      :selected-cell="selectedCell"
      :game-over="gameOver"
      @update-cell="updateCell"
    />

    <v-alert
      v-if="isComplete"
      type="success"
      class="mt-4"
      title="恭喜!"
      text="你已經完成了數獨遊戲!"
    ></v-alert>

    <v-alert
      v-if="!isValid && !isComplete && !gameOver"
      type="error"
      class="mt-4"
      title="錯誤"
      text="目前的數獨狀態不符合規則，請檢查您的輸入。"
    ></v-alert>

    <v-alert
      v-if="gameOver"
      type="error"
      class="mt-4"
      title="遊戲結束"
      text="已達到最大錯誤次數 (3次)，遊戲結束!"
    ></v-alert>
  </div>
</template>

<style scoped>
.sudoku-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.game-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 400px;
}

.game-timer, .remaining-errors {
  font-weight: bold;
}

.sudoku-board {
  background-color: white !important;
  padding: 8px;
  border-radius: 8px;
}

.board-grid {
  display: flex;
  flex-direction: column;
}

.board-row {
  display: flex;
}

/* Add this to ensure the board is visible */
:deep(.v-card) {
  background-color: white !important;
  color: black !important;
}
</style>
